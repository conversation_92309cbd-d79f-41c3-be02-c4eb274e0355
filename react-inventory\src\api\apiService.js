import axios from 'axios';

// Create an Axios instance with the base URL for your Laravel API
export const api = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000/api',
    headers: {
        'Content-Type': 'application/json',
    },
});

// Add an interceptor to include the token in the request headers if it exists
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('authToken'); // Retrieve token from localStorage
        if (token) {
            config.headers.Authorization = `Bearer ${token}`; // Set the Authorization header
        }
        return config; // Return the modified config
    },
    (error) => Promise.reject(error) // Handle request error
);

// Add an interceptor to handle responses and errors
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response) {
            // Handle specific status codes
            switch (error.response.status) {
                case 403:
                    console.error('Forbidden: Access denied.');
                    break;
                case 401:
                    console.error('Unauthorized access - Token may be invalid or expired.');
                    // Clear invalid token from localStorage
                    localStorage.removeItem('authToken');
                    // Optionally redirect to login page
                    // window.location.href = '/login';
                    break;
                case 404:
                    console.error('Not Found.');
                    break;
                case 409:
                    console.error('Conflict: This email is already registered.');
                    break;
                case 422:
                    // Handle validation errors, like email already taken
                    if (error.response.data.errors && error.response.data.errors.email) {
                        console.error('Validation Error: Email already taken.');
                    } else {
                        console.error('Validation Error.');
                    }
                    break;
                default:
                    console.error('An error occurred.');
            }
        } else {
            console.error('No response received.');
        }
        return Promise.reject(error);
    }
);
