[2025-07-10 02:42:29] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:43:42] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:16] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:17] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:18] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:19] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:20] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:21] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:23] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:24] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:24] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:25] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:26] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:47:26] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:51:25] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#46 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\app\\Http\\Controllers\\LoginController.php(20): Illuminate\\Database\\Eloquent\\Builder->first()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\LoginController->login(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LoginController), 'login')
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}
"} 
[2025-07-10 02:52:09] production.ERROR: Database file at path [C:\Users\<USER>\Documents\GitHub\fullstack_inventory-react_laravel\laravel-inventory\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select exists (select 1 from sqlite_master where name = 'migrations' and type = 'table') as "exists") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select exists (select 1 from sqlite_master where name = 'migrations' and type = 'table') as \"exists\") at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\SQLiteBuilder.php(44): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\SQLiteBuilder->hasTable('migrations')
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(68): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\FreshCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(67): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:37)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(224): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#6 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\SQLiteBuilder.php(44): Illuminate\\Database\\Connection->scalar('select exists (...')
#12 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\SQLiteBuilder->hasTable('migrations')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(742): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(68): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\FreshCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(67): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#18 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#23 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\GitHub\\fullstack_inventory-react_laravel\\laravel-inventory\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}
"} 
