import React, { useState, useEffect } from 'react'
import { <PERSON>rows<PERSON><PERSON><PERSON><PERSON>, Routes, Route, Navigate } from 'react-router-dom'
import { Welcome, Approval, NonFound, TermsOfUse, PrivacyPolicy } from './components'
import { Dashboard, Inventory, Receipt, HelpSupport, Settings } from './components/pages'
import { Layout } from './components/pages/shared'
import { Registration, Login, VerificationPage, SetPassword } from './components/auth'
import { api } from './api/apiService'

function App() {
  const PrivateRoute = ({ children, requiresAuth, requiresPasswordSet, requiresEmailVerified, requiresAdmin, requiresApproval }) => {
    const [loading, setLoading] = useState(true)
    const [isAuthenticated, setIsAuthenticated] = useState(false)
    const [emailVerified, setEmailVerified] = useState(false)
    const [passwordSet, setPasswordSet] = useState(false)
    const [isApproved, setIsApproved] = useState(false)
    const [userStatus, setUserStatus] = useState(null)
    const [isAdmin, setIsAdmin] = useState(false)
  
    useEffect(() => {
      const checkAuthStatus = async () => {
        try {
          const userResponse = await api.get('/user-status')
          const status = userResponse.data.status
          const isAdmin = userResponse.data.is_admin

          // Set user authentication state
          setIsAuthenticated(userResponse.data.authenticated)
          setIsAdmin(isAdmin)

          // Handle inactive user logic
          if (status === 'pending') {
            setUserStatus(status)
            setIsApproved(false)
            return
          }

          // Check if authentication is valid
          const authResponse = await api.get('/auth/check')
          setIsAuthenticated(authResponse.data.authenticated)

          if (authResponse.data.authenticated) {
            const emailResponse = await api.get('/get-email')
            setEmailVerified(!!emailResponse.data.email)

            const passwordResponse = await api.get('/auth/password-set')
            setPasswordSet(passwordResponse.data.passwordSet)
    
            // Determine approval status based on requiresApproval
            if (requiresApproval) {
              setIsApproved(status === 'active' && isAdmin)
            } else {
              setIsApproved(true) // If approval is not required, set approved to true
            }
          }
        } catch (error) {
          console.error('Error fetching auth status:', error)
          setIsAuthenticated(false)
          setEmailVerified(false)
          setPasswordSet(false)
          setIsApproved(false)
          setIsAdmin(false)
          setUserStatus(null)
        } finally {
          setLoading(false)
        }
      }
    
      checkAuthStatus()
    }, [requiresApproval])    
  
    if (loading) return <div>Loading...</div>
  
    // Redirect logic based on user status
    if (userStatus === 'pending') return <Navigate to="/approval" />
    if (requiresEmailVerified && !emailVerified) return <Navigate to="/register/verification-page" />
    if (requiresPasswordSet && !passwordSet) return <Navigate to="/register/verification-page/set-password" />
    if (requiresAuth && !isAuthenticated) return <Navigate to="/login" />
    if (requiresApproval && !isApproved) return <Navigate to="/approval" />
    if (requiresAdmin && !isAdmin) return <Navigate to="/settings" />
  
    return children
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/welcome" element={<Welcome />} />
        <Route path="/register" element={<Registration />} />
        <Route path="/terms-of-use" element={<TermsOfUse />} />
        <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        <Route path="/register/verification-page/:token" 
          element={
            <PrivateRoute requiresAuth={false}>
              <VerificationPage />
            </PrivateRoute>
          } 
        />
        <Route path="/register/verification-page/:token/:verificationCode" 
          element={
            <PrivateRoute requiresAuth={false}>
              <SetPassword />
            </PrivateRoute>
          } 
        />
        <Route path="/login" element={<Login />} />
        <Route
          path="/approval"
          element={
            <PrivateRoute requiresAuth={false} requiresApproval={false}>
              <Approval />
            </PrivateRoute>
          }
        />
        <Route path="/" element={<Layout />}>
          <Route
            index
            element={
              <PrivateRoute requiresAuth={false} requiresApproval={false}>
                <Dashboard />
              </PrivateRoute>
            }
          />
          <Route
            path="inventory"
            element={
              <PrivateRoute requiresAuth={false} requiresApproval={false}>
                <Inventory />
              </PrivateRoute>
            }
          />
         {/*  <Route
            path="purchase"
            element={
              <PrivateRoute requiresAuth={false} requiresApproval={false}>
                <Purchase />
              </PrivateRoute>
            }
          /> */}
          <Route
            path="/receipt"
            element={
              <PrivateRoute requiresAuth={false} requiresApproval={false}>
                <Receipt />
              </PrivateRoute>
            }
          />
          <Route
            path="/help-support"
            element={
              <PrivateRoute requiresAuth={false} requiresApproval={false}>
                <HelpSupport />
              </PrivateRoute>
            }
          />
          <Route
            path="/settings"
            element={
              <PrivateRoute requiresAuth={false} requiresApproval={false} requiresAdmin={false}>
                <Settings />
              </PrivateRoute>
            }
          />
        </Route>
        <Route path="/*" element={<NonFound />} />
      </Routes>
    </BrowserRouter>
  )
}

export default App
